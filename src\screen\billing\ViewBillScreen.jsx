import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, Button } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import {
    BluetoothManager,
    BluetoothEscposPrinter,
} from 'react-native-bluetooth-escpos-printer';
import { Picker } from '@react-native-picker/picker';




const ViewBillScreen = ({ route }) => {

    const navigation = useNavigation();

    const {
        saleID,
        printDateTime,
        currentTimeStamp,
        itemTableDetails,
        deliveryCharges,
        roundOffAmount,
        totalAmount,
        discountAmount,
        selectedCustomerType,
        selectedGatewayName,
        customerID,
        customerName,
        customerPhoneNumber,
        customerAddress,
        customerPlace,
        customerCity,
        customerState,
        customerPincode,
        branchId,
        branchName,
        areaName,
        gstNumber,
        foodLicenseNumber,
        branchPincode,
    } = route.params;

    const hideNosColumn = itemTableDetails.every(item => (item.AltQty ?? 0) === 0);
    const hideWeightColumn = itemTableDetails.every(item => (item.Qty ?? 0) === 0);
    const hideGSTColumn = itemTableDetails.every(item => (item.TaxAmount ?? 0) === 0);

    const calculateTaxDetails = (items) => {
        const taxBreakups = [];

        items.forEach(item => {
            const cgstPercent = item.CGSTPercent || 0;
            const sgstPercent = item.SGSTPercent || 0;
            const cgstAmount = item.CGSTAmount || 0;
            const sgstAmount = item.SGSTAmount || 0;

            if (cgstAmount !== 0 || sgstAmount !== 0) {
                taxBreakups.push({
                    cgstPercent,
                    sgstPercent,
                    cgstAmount,
                    sgstAmount,
                });
            }
        });

        return { taxBreakups };
    };

    const taxDetails = calculateTaxDetails(itemTableDetails);

    const [bluetoothDevices, setBluetoothDevices] = useState([]);
    const [selectedDevice, setSelectedDevice] = useState(null);
    const scanBluetoothDevices = async () => {
        try {
            const isEnabled = await BluetoothManager.isBluetoothEnabled();
            if (!isEnabled) {
                await BluetoothManager.enableBluetooth();
            }
            const devices = await BluetoothManager.scanDevices();
            const parsed = JSON.parse(devices.found); // List of bonded devices
            setBluetoothDevices(parsed);
        } catch (e) {
            console.log("Error scanning devices:", e);
        }
    };
    const connectPrinter = async (device) => {
        try {
            await BluetoothManager.connect(device.address);
            setSelectedDevice(device);
            Alert.alert('Connected', `Connected to ${device.name}`);
        } catch (e) {
            Alert.alert('Connection failed', e.message);
        }
    };
    const printReceipt = async () => {
    let receiptText = "";

    // Build receipt text
    receiptText += "ABIS Exports\n";
    receiptText += "India Pvt Ltd\n";
    receiptText += `${branchId}, ${branchName}\n`;
    receiptText += `${areaName}, ${branchPincode}\n`;
    receiptText += `FSSAI: ${foodLicenseNumber}, GSTIN: ${gstNumber}\n`;
    receiptText += "-------------------------------\n";

    receiptText += `POS/Bill No: ${saleID}\n`;
    receiptText += `Print Date: ${printDateTime}\n`;
    receiptText += `Bus. Date: ${currentTimeStamp}\n`;
    receiptText += "-------------------------------\n";

    receiptText += "Item Details\n";
    for (let item of itemTableDetails) {
        const qty = item.Qty !== 0 ? item.Qty : item.AltQty;
        const gst = hideGSTColumn ? "" : `GST: ₹${item.TaxAmount}`;
        receiptText += `${item.ItemName}\nQty: ${qty}, Rate: ₹${item.Rate}, ${gst} Total: ₹${item.TotalAmount}\n`;
    }

    receiptText += "-------------------------------\n";
    if (deliveryCharges > 0) {
        receiptText += `Freight: ₹${deliveryCharges}\n`;
    }
    receiptText += `Round-off: ₹${roundOffAmount}\n`;
    receiptText += `Total: ₹${totalAmount}\n`;

    if (selectedCustomerType !== "CS") {
        receiptText += `${selectedGatewayName}: ₹${totalAmount}\n`;
    }

    if (discountAmount !== '0') {
        receiptText += `You saved ₹${discountAmount}\n`;
    }

    if (taxDetails.taxBreakups.length > 0) {
        receiptText += "CGST  SGST  CGSTAmt  SGSTAmt\n";
        for (let tax of taxDetails.taxBreakups) {
            receiptText += `${tax.cgstPercent}%  ${tax.sgstPercent}%  ₹${tax.cgstAmount.toFixed(2)}  ₹${tax.sgstAmount.toFixed(2)}\n`;
        }
    }

    receiptText += "-------------------------------\n";
    receiptText += `Customer ID: ${customerID}\n`;
    receiptText += `Name: ${customerName}, Phone: ${customerPhoneNumber}\n`;
    if (customerAddress) {
        receiptText += `Address: ${customerAddress}\n`;
    }
    if (customerPlace || customerCity) {
        receiptText += `${customerPlace} ${customerCity}\n`;
    }
    if (customerState || customerPincode) {
        receiptText += `${customerState} ${customerPincode}\n`;
    }

    receiptText += "\nThanks & Visit Again!\n\n\n";

    // ✅ Print receipt text to console
    console.log("🧾 Receipt Content:\n" + receiptText);

    // Optionally send to printer if a device is selected
    if (!selectedDevice) {
        Alert.alert("No printer selected");
        return;
    }

    try {
        await BluetoothEscposPrinter.printText(receiptText, {
            encoding: 'GBK',
            codepage: 0,
            widthtimes: 0,
            heigthtimes: 0,
            fonttype: 0,
        });
    } catch (e) {
        Alert.alert("Print Error", e.message);
        console.log("Printing error", e);
    }
};



    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.navigate('Billing')}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>


            <Text style={styles.title}>ABIS Exports India Pvt Ltd</Text>
            <Text style={styles.center}>{branchId}, {branchName}</Text>
            <Text style={styles.center}>{areaName}</Text>
            <Text style={styles.center}>{branchPincode}</Text>
            <Text style={styles.center}>FSSAI License Number: {foodLicenseNumber}, GSTIN : {gstNumber}</Text>


            <View style={styles.rowSpaceBetween}>
                <Text>POS/Bill No: {saleID}</Text>
                <Text>Print Date: {printDateTime}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Business Date: {currentTimeStamp}</Text>
            </View>

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={styles.cell}>Item name</Text>
                    <Text style={styles.cell}>Qty</Text>
                    <Text style={styles.cell}>Rate</Text>
                    {!hideGSTColumn && <Text style={styles.cell}>GST</Text>}
                    <Text style={styles.cell}>Total</Text>
                </View>
                {itemTableDetails.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={styles.cell}>{item.ItemName}</Text>
                        <Text style={styles.cell}>{item.Qty !== 0 ? item.Qty : item.AltQty}</Text>
                        <Text style={styles.cell}>{item.Rate}</Text>
                        {!hideGSTColumn && <Text style={styles.cell}>{item.TaxAmount}</Text>}
                        <Text style={styles.cell}>{item.TotalAmount}</Text>
                    </View>
                ))}
            </View>

            {deliveryCharges !== 0 && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Freight:</Text>
                    <Text>₹{deliveryCharges}</Text>
                </View>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>Roundoff:</Text>
                <Text>₹{roundOffAmount}</Text>
            </View>

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>Total (incl. GST):</Text>
                <Text style={styles.bold}>₹{totalAmount}</Text>
            </View>

            {selectedCustomerType !== 'CS' && (
                <>
                    {selectedGatewayName === 'Cash' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>Cash Paid:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                    {selectedGatewayName === 'UPI' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>UPI:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                    {selectedGatewayName === 'Debit Card' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>Debit Card:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                </>
            )}

            {(discountAmount !== '0' || taxDetails.taxBreakups.length > 0) && <View style={styles.divider} />}

            {discountAmount !== '0' && (
                <View style={styles.rowCenter}>
                    <Text>You saved ₹{discountAmount}</Text>
                </View>
            )}

            {taxDetails.taxBreakups.length > 0 && (
                <>
                    <View style={styles.rowCenter}>
                        <Text style={styles.cell}>CGST</Text>
                        <Text style={styles.cell}>SGST</Text>
                        <Text style={styles.cell}>CGST Amt</Text>
                        <Text style={styles.cell}>SGST Amt</Text>
                    </View>
                    {taxDetails.taxBreakups.map((tax, i) => (
                        <View key={i} style={styles.rowCenter}>
                            <Text style={styles.cell}>{tax.cgstPercent}%</Text>
                            <Text style={styles.cell}>{tax.sgstPercent}%</Text>
                            <Text style={styles.cell}>₹{tax.cgstAmount.toFixed(2)}</Text>
                            <Text style={styles.cell}>₹{tax.sgstAmount.toFixed(2)}</Text>
                        </View>
                    ))}
                </>
            )}

            <Text style={styles.sectionHeader}>Customer Details:</Text>
            <Text>Customer ID: {customerID}</Text>
            <Text>Customer Name: {customerName}</Text>
            <Text>Mobile: {customerPhoneNumber}</Text>
            {customerAddress?.length > 0 && <Text>Address: {customerAddress}</Text>}
            {(customerPlace || customerCity) && <Text>{customerPlace} {customerCity}</Text>}
            {(customerState || customerPincode) && <Text>{customerState} {customerPincode}</Text>}

            <View style={styles.divider} />
            <View style={{ marginTop: 20, alignItems: 'center' }}>
                <Text style={{ textAlign: 'center', fontSize: 16, marginBottom: 10 }}>
                    Thanks & Visit Again!
                </Text>

                <Picker
                    selectedValue={selectedDevice?.address}
                    onValueChange={(address) => {
                        const device = bluetoothDevices.find(d => d.address === address);
                        if (device) connectPrinter(device);
                    }}
                    style={{ width: '90%', marginVertical: 10 }}
                >
                    {bluetoothDevices.map((device, index) => (
                        <Picker.Item label={device.name} value={device.address} key={index} />
                    ))}
                </Picker>
            </View>

            {/* Button Row at the Bottom */}
            <View
                style={{
                    position: 'absolute',
                    bottom: 20,
                    left: 0,
                    right: 0,
                    flexDirection: 'row',
                    justifyContent: 'space-evenly',
                    paddingHorizontal: 20,
                }}
            >
                <Button title="Print" onPress={printReceipt} />
                <Button title="Refresh" onPress={scanBluetoothDevices} />
            </View>


        </ScrollView>

    );
};

export default ViewBillScreen;

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },


    container: { padding: 16 },
    title: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' },
    center: { textAlign: 'center' },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: { flexDirection: 'row', justifyContent: 'flex-end' },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
    },
    table: {
        borderWidth: 1,
        marginTop: 8,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
    },
    cell: {
        flex: 1,
        padding: 6,
        textAlign: 'center',
        borderRightWidth: 1,
    },
    bold: { fontWeight: 'bold' },
    rowCenter: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
    },
});
